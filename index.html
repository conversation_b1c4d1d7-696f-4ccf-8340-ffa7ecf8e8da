<!DOCTYPE html>
<html lang="en">

<head>
     <meta charset="UTF-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0">
     <title>PepMove Logistics Role Mapping Survey</title>
     <style>
          /* PepMove Brand Colors */
          :root {
               --primary-color: #8DC63F;
               /* PepMove Green */
               --secondary-color: #4A4A4A;
               /* Dark Gray */
               --accent-color: #000000;
               /* Black */
               --text-color: #333333;
               --border-color: #e5e7eb;
               --bg-light: #f9fafb;
               --header-bg: #e8f5e8;
          }

          @media print {
               body {
                    margin: 0;
                    font-size: 10pt;
               }

               .no-print {
                    display: none !important;
               }

               .page-break {
                    page-break-before: always;
               }

               table {
                    page-break-inside: avoid;
               }

               h2 {
                    page-break-after: avoid;
               }

               .section {
                    page-break-inside: avoid;
               }
          }

          * {
               box-sizing: border-box;
          }

          body {
               font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON>l, sans-serif;
               max-width: 8.5in;
               margin: 0 auto;
               padding: 20px;
               font-size: 11pt;
               line-height: 1.4;
               color: var(--text-color);
               background: #f5f5f5;
          }

          .container {
               background: white;
               padding: 40px;
               border-radius: 10px;
               box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          }

          .header {
               text-align: center;
               margin-bottom: 30px;
               padding-bottom: 20px;
               border-bottom: 3px solid var(--primary-color);
          }

          .logo-container {
               margin-bottom: 20px;
          }

          .logo {
               height: 80px;
               width: auto;
          }

          h1 {
               color: var(--secondary-color);
               font-size: 28pt;
               margin: 0;
          }

          .subtitle {
               color: #6b7280;
               margin-top: 10px;
          }

          h2 {
               color: var(--secondary-color);
               font-size: 16pt;
               margin-top: 30px;
               margin-bottom: 15px;
               border-bottom: 2px solid var(--primary-color);
               padding-bottom: 8px;
          }

          .form-field {
               margin: 15px 0;
          }

          .form-field label {
               font-weight: 600;
               display: block;
               margin-bottom: 5px;
               color: var(--text-color);
          }

          .form-field input[type="text"],
          .form-field input[type="email"] {
               width: 100%;
               max-width: 400px;
               padding: 10px;
               border: 2px solid var(--border-color);
               border-radius: 5px;
               font-size: 11pt;
               transition: border-color 0.3s;
          }

          .form-field input:focus {
               outline: none;
               border-color: var(--primary-color);
          }

          .instructions {
               font-style: italic;
               color: #6b7280;
               margin: 10px 0;
          }

          table {
               width: 100%;
               border-collapse: collapse;
               margin: 15px 0;
               font-size: 10pt;
          }

          th {
               background-color: var(--header-bg);
               font-weight: bold;
               padding: 10px 5px;
               text-align: center;
               border: 1px solid var(--border-color);
          }

          td {
               border: 1px solid var(--border-color);
               padding: 8px 5px;
               text-align: center;
          }

          td:first-child {
               text-align: left;
               font-weight: 500;
               padding-left: 10px;
          }

          input[type="radio"],
          input[type="checkbox"] {
               cursor: pointer;
               transform: scale(1.2);
          }

          .checkbox-grid {
               display: grid;
               grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
               gap: 10px;
               margin: 15px 0;
          }

          .checkbox-item {
               display: flex;
               align-items: center;
               padding: 5px;
          }

          .checkbox-item input {
               margin-right: 10px;
          }

          textarea {
               width: 100%;
               min-height: 120px;
               padding: 10px;
               border: 2px solid var(--border-color);
               border-radius: 5px;
               font-family: inherit;
               font-size: 11pt;
               resize: vertical;
          }

          textarea:focus {
               outline: none;
               border-color: var(--primary-color);
          }

          .button-container {
               text-align: center;
               margin: 40px 0;
          }

          .submit-button {
               background-color: var(--primary-color);
               color: white;
               padding: 15px 40px;
               border: none;
               border-radius: 5px;
               font-size: 16pt;
               font-weight: bold;
               cursor: pointer;
               transition: all 0.3s;
          }

          .submit-button:hover {
               background-color: #7DB532;
               transform: translateY(-2px);
               box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
          }

          .submit-button:disabled {
               background-color: #9ca3af;
               cursor: not-allowed;
               transform: none;
          }

          .loading {
               display: none;
               text-align: center;
               margin: 20px 0;
          }

          .spinner {
               border: 3px solid var(--border-color);
               border-top: 3px solid var(--primary-color);
               border-radius: 50%;
               width: 40px;
               height: 40px;
               animation: spin 1s linear infinite;
               margin: 0 auto;
          }

          @keyframes spin {
               0% {
                    transform: rotate(0deg);
               }

               100% {
                    transform: rotate(360deg);
               }
          }

          .success-message {
               display: none;
               background-color: #d1fae5;
               color: #065f46;
               padding: 20px;
               border-radius: 5px;
               text-align: center;
               margin: 20px 0;
          }

          .error-message {
               display: none;
               background-color: #fee2e2;
               color: #991b1b;
               padding: 20px;
               border-radius: 5px;
               text-align: center;
               margin: 20px 0;
          }

          .section {
               margin: 30px 0;
          }

          /* Responsive */
          @media (max-width: 768px) {
               body {
                    padding: 10px;
               }

               .container {
                    padding: 20px;
               }

               h1 {
                    font-size: 22pt;
               }

               table {
                    font-size: 9pt;
               }

               th,
               td {
                    padding: 5px 2px;
               }
          }
     </style>
</head>

<body>
     <div class="container">
          <div class="header">
               <div class="logo-container">
                    <svg class="logo" viewBox="0 0 300 100" xmlns="http://www.w3.org/2000/svg">
                         <!-- PepMove Logo Recreation -->
                         <circle cx="50" cy="50" r="45" fill="#8DC63F" />
                         <circle cx="35" cy="35" r="8" fill="#000" />
                         <circle cx="65" cy="35" r="8" fill="#000" />
                         <circle cx="35" cy="65" r="8" fill="#000" />
                         <circle cx="65" cy="65" r="8" fill="#000" />
                         <circle cx="50" cy="50" r="8" fill="#000" />
                         <text x="110" y="55" font-family="Arial, sans-serif" font-size="36" font-weight="bold"
                              fill="#000">PEPMOVE</text>
                         <text x="110" y="75" font-family="Arial, sans-serif" font-size="12" fill="#4A4A4A">PRECISION
                              EQUIPMENT PLACEMENT</text>
                    </svg>
               </div>
               <h1>Logistics Role Mapping Survey</h1>
               <p class="subtitle">Help us understand your role and improve our operations</p>
          </div>

          <!-- Update this form action with your email -->
          <form id="surveyForm" action="https://formsubmit.co/<EMAIL>" method="POST">
               <!-- FormSubmit Configuration -->
               <input type="hidden" name="_subject" value="New PepMove Survey Submission">
               <input type="hidden" name="_captcha" value="false">
               <input type="hidden" name="_template" value="table">

               <div class="section">
                    <h2>Personal Information</h2>

                    <div class="form-field">
                         <label for="name">Name *</label>
                         <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-field">
                         <label for="email">Email Address *</label>
                         <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-field">
                         <label for="jobTitle">Job Title *</label>
                         <input type="text" id="jobTitle" name="job_title" required>
                    </div>
               </div>

               <div class="section">
                    <h2>Task Frequency Assessment</h2>
                    <p class="instructions">How often do you perform each of the following tasks?</p>

                    <table>
                         <thead>
                              <tr>
                                   <th style="width: 40%">Task</th>
                                   <th>Never</th>
                                   <th>Rarely<br>(Monthly)</th>
                                   <th>Sometimes<br>(Weekly)</th>
                                   <th>Often<br>(2-3x/Week)</th>
                                   <th>Daily</th>
                              </tr>
                         </thead>
                         <tbody>
                              <tr>
                                   <td>Labeling & documentation (shipping paperwork)</td>
                                   <td><input type="radio" name="freq_labeling" value="Never"></td>
                                   <td><input type="radio" name="freq_labeling" value="Rarely"></td>
                                   <td><input type="radio" name="freq_labeling" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_labeling" value="Often"></td>
                                   <td><input type="radio" name="freq_labeling" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Warehouse operations & material handling</td>
                                   <td><input type="radio" name="freq_warehouse" value="Never"></td>
                                   <td><input type="radio" name="freq_warehouse" value="Rarely"></td>
                                   <td><input type="radio" name="freq_warehouse" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_warehouse" value="Often"></td>
                                   <td><input type="radio" name="freq_warehouse" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Carrier coordination (quotes, pickups, claims)</td>
                                   <td><input type="radio" name="freq_carrier" value="Never"></td>
                                   <td><input type="radio" name="freq_carrier" value="Rarely"></td>
                                   <td><input type="radio" name="freq_carrier" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_carrier" value="Often"></td>
                                   <td><input type="radio" name="freq_carrier" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Quality inspections & reverse logistics</td>
                                   <td><input type="radio" name="freq_quality" value="Never"></td>
                                   <td><input type="radio" name="freq_quality" value="Rarely"></td>
                                   <td><input type="radio" name="freq_quality" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_quality" value="Often"></td>
                                   <td><input type="radio" name="freq_quality" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>System & data management (ERP/WMS)</td>
                                   <td><input type="radio" name="freq_systems" value="Never"></td>
                                   <td><input type="radio" name="freq_systems" value="Rarely"></td>
                                   <td><input type="radio" name="freq_systems" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_systems" value="Often"></td>
                                   <td><input type="radio" name="freq_systems" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Reporting & analytics (costs, metrics)</td>
                                   <td><input type="radio" name="freq_reporting" value="Never"></td>
                                   <td><input type="radio" name="freq_reporting" value="Rarely"></td>
                                   <td><input type="radio" name="freq_reporting" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_reporting" value="Often"></td>
                                   <td><input type="radio" name="freq_reporting" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Safety & compliance (HAZMAT, SOPs)</td>
                                   <td><input type="radio" name="freq_safety" value="Never"></td>
                                   <td><input type="radio" name="freq_safety" value="Rarely"></td>
                                   <td><input type="radio" name="freq_safety" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_safety" value="Often"></td>
                                   <td><input type="radio" name="freq_safety" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Cross-functional coordination (vendors/internal teams)</td>
                                   <td><input type="radio" name="freq_coordination" value="Never"></td>
                                   <td><input type="radio" name="freq_coordination" value="Rarely"></td>
                                   <td><input type="radio" name="freq_coordination" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_coordination" value="Often"></td>
                                   <td><input type="radio" name="freq_coordination" value="Daily"></td>
                              </tr>
                              <tr>
                                   <td>Continuous improvement (process optimization)</td>
                                   <td><input type="radio" name="freq_improvement" value="Never"></td>
                                   <td><input type="radio" name="freq_improvement" value="Rarely"></td>
                                   <td><input type="radio" name="freq_improvement" value="Sometimes"></td>
                                   <td><input type="radio" name="freq_improvement" value="Often"></td>
                                   <td><input type="radio" name="freq_improvement" value="Daily"></td>
                              </tr>
                         </tbody>
                    </table>
               </div>

               <div class="section page-break">
                    <h2>Task Comfort Level Assessment</h2>
                    <p class="instructions">How comfortable are you with performing each of the following tasks?</p>

                    <table>
                         <thead>
                              <tr>
                                   <th style="width: 40%">Task</th>
                                   <th>Very<br>Uncomfortable</th>
                                   <th>Somewhat<br>Uncomfortable</th>
                                   <th>Neutral</th>
                                   <th>Comfortable</th>
                                   <th>Very<br>Comfortable</th>
                              </tr>
                         </thead>
                         <tbody>
                              <tr>
                                   <td>Labeling & documentation (shipping paperwork)</td>
                                   <td><input type="radio" name="comfort_labeling" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_labeling" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_labeling" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_labeling" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_labeling" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Warehouse operations & material handling</td>
                                   <td><input type="radio" name="comfort_warehouse" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_warehouse" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_warehouse" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_warehouse" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_warehouse" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Carrier coordination (quotes, pickups, claims)</td>
                                   <td><input type="radio" name="comfort_carrier" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_carrier" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_carrier" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_carrier" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_carrier" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Quality inspections & reverse logistics</td>
                                   <td><input type="radio" name="comfort_quality" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_quality" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_quality" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_quality" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_quality" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>System & data management (ERP/WMS)</td>
                                   <td><input type="radio" name="comfort_systems" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_systems" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_systems" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_systems" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_systems" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Reporting & analytics (costs, metrics)</td>
                                   <td><input type="radio" name="comfort_reporting" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_reporting" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_reporting" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_reporting" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_reporting" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Safety & compliance (HAZMAT, SOPs)</td>
                                   <td><input type="radio" name="comfort_safety" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_safety" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_safety" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_safety" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_safety" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Cross-functional coordination (vendors/internal teams)</td>
                                   <td><input type="radio" name="comfort_coordination" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_coordination" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="comfort_coordination" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_coordination" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_coordination" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Continuous improvement (process optimization)</td>
                                   <td><input type="radio" name="comfort_improvement" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="comfort_improvement" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="comfort_improvement" value="Neutral"></td>
                                   <td><input type="radio" name="comfort_improvement" value="Comfortable"></td>
                                   <td><input type="radio" name="comfort_improvement" value="Very Comfortable"></td>
                              </tr>
                         </tbody>
                    </table>
               </div>

               <div class="section page-break-before">
                    <h2>Market Coverage</h2>
                    <p class="instructions">Which markets do you currently support? (Check all that apply)</p>

                    <div class="checkbox-grid">
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_bna" name="markets" value="BNA (Nashville)">
                              <label for="market_bna">BNA (Nashville)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_nola" name="markets" value="NOLA (New Orleans)">
                              <label for="market_nola">NOLA (New Orleans)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_rdu" name="markets" value="RDU (Raleigh-Durham)">
                              <label for="market_rdu">RDU (Raleigh-Durham)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_co" name="markets" value="Colorado">
                              <label for="market_co">Colorado</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_or" name="markets" value="Oregon">
                              <label for="market_or">Oregon</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_wa" name="markets" value="Washington">
                              <label for="market_wa">Washington</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_spokane" name="markets" value="Spokane">
                              <label for="market_spokane">Spokane</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_ut" name="markets" value="Utah">
                              <label for="market_ut">Utah</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_atl" name="markets" value="Atlanta">
                              <label for="market_atl">Atlanta</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="market_mem" name="markets" value="Memphis">
                              <label for="market_mem">Memphis</label>
                         </div>
                    </div>
               </div>

               <div class="section">
                    <h2>Market Comfort Level</h2>
                    <p class="instructions">How comfortable are you working with each market?</p>

                    <table>
                         <thead>
                              <tr>
                                   <th style="width: 40%">Market</th>
                                   <th>Very<br>Uncomfortable</th>
                                   <th>Somewhat<br>Uncomfortable</th>
                                   <th>Neutral</th>
                                   <th>Comfortable</th>
                                   <th>Very<br>Comfortable</th>
                              </tr>
                         </thead>
                         <tbody>
                              <tr>
                                   <td>BNA (Nashville)</td>
                                   <td><input type="radio" name="market_comfort_bna" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_bna" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_bna" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_bna" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_bna" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>NOLA (New Orleans)</td>
                                   <td><input type="radio" name="market_comfort_nola" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_nola" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_nola" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_nola" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_nola" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>RDU (Raleigh-Durham)</td>
                                   <td><input type="radio" name="market_comfort_rdu" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_rdu" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_rdu" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_rdu" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_rdu" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Colorado</td>
                                   <td><input type="radio" name="market_comfort_co" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_co" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_co" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_co" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_co" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Oregon</td>
                                   <td><input type="radio" name="market_comfort_or" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_or" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_or" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_or" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_or" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Washington</td>
                                   <td><input type="radio" name="market_comfort_wa" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_wa" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_wa" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_wa" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_wa" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Spokane</td>
                                   <td><input type="radio" name="market_comfort_spokane" value="Very Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_spokane" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_spokane" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_spokane" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_spokane" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Utah</td>
                                   <td><input type="radio" name="market_comfort_ut" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_ut" value="Somewhat Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_ut" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_ut" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_ut" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Atlanta</td>
                                   <td><input type="radio" name="market_comfort_atl" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_atl" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_atl" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_atl" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_atl" value="Very Comfortable"></td>
                              </tr>
                              <tr>
                                   <td>Memphis</td>
                                   <td><input type="radio" name="market_comfort_mem" value="Very Uncomfortable"></td>
                                   <td><input type="radio" name="market_comfort_mem" value="Somewhat Uncomfortable">
                                   </td>
                                   <td><input type="radio" name="market_comfort_mem" value="Neutral"></td>
                                   <td><input type="radio" name="market_comfort_mem" value="Comfortable"></td>
                                   <td><input type="radio" name="market_comfort_mem" value="Very Comfortable"></td>
                              </tr>
                         </tbody>
                    </table>
               </div>

               <div class="section">
                    <h2>Work Schedule</h2>
                    <p class="instructions">When do you typically perform logistics tasks? (Check all that apply)</p>

                    <div class="checkbox-grid">
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_early" name="schedule" value="Early Morning (5-8 AM)">
                              <label for="schedule_early">Early Morning (5:00 AM - 8:00 AM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_morning" name="schedule" value="Morning (8-12 PM)">
                              <label for="schedule_morning">Morning (8:00 AM - 12:00 PM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_afternoon" name="schedule"
                                   value="Afternoon (12-5 PM)">
                              <label for="schedule_afternoon">Afternoon (12:00 PM - 5:00 PM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_evening" name="schedule" value="Evening (5-8 PM)">
                              <label for="schedule_evening">Evening (5:00 PM - 8:00 PM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_night" name="schedule" value="Night (8 PM-12 AM)">
                              <label for="schedule_night">Night (8:00 PM - 12:00 AM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_overnight" name="schedule"
                                   value="Overnight (12-5 AM)">
                              <label for="schedule_overnight">Overnight (12:00 AM - 5:00 AM)</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_weekends" name="schedule" value="Weekends">
                              <label for="schedule_weekends">Weekends</label>
                         </div>
                         <div class="checkbox-item">
                              <input type="checkbox" id="schedule_oncall" name="schedule" value="On-call/As needed">
                              <label for="schedule_oncall">On-call/As needed</label>
                         </div>
                    </div>
               </div>

               <div class="section">
                    <h2>Support & Tools</h2>
                    <p class="instructions">What types of support, tools, or resources would help you perform your job
                         more effectively?</p>

                    <textarea name="support_tools" id="support_tools"
                         placeholder="Please describe any tools, training, or support that would be helpful..."></textarea>
               </div>

               <div class="section">
                    <h2>Additional Comments</h2>
                    <p class="instructions">Please share any additional information about your role, challenges, or
                         suggestions:</p>

                    <textarea name="comments" id="comments" placeholder="Your feedback is valuable to us..."></textarea>
               </div>

               <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Submitting your survey...</p>
               </div>

               <div class="success-message" id="successMessage">
                    <h3>✅ Survey Submitted Successfully!</h3>
                    <p>Thank you for your participation. Your responses have been recorded.</p>
               </div>

               <div class="error-message" id="errorMessage">
                    <h3>❌ Submission Error</h3>
                    <p>There was an error submitting your survey. Please try again or contact support.</p>
               </div>

               <div class="button-container">
                    <button type="submit" class="submit-button" id="submitBtn">Submit Survey</button>
               </div>
          </form>
     </div>

     <script>
          // Form validation and submission handling
          document.getElementById('surveyForm').addEventListener('submit', function (e) {
               const submitBtn = document.getElementById('submitBtn');
               const loading = document.getElementById('loading');

               // Show loading state
               submitBtn.disabled = true;
               loading.style.display = 'block';

               // Form will submit normally to FormSubmit
          });

          // Auto-save functionality
          const form = document.getElementById('surveyForm');
          const inputs = form.querySelectorAll('input, textarea');

          // Save form data to localStorage
          function saveFormData() {
               const formData = new FormData(form);
               const data = {};

               for (let [key, value] of formData.entries()) {
                    if (data[key]) {
                         // Handle multiple values (checkboxes)
                         if (Array.isArray(data[key])) {
                              data[key].push(value);
                         } else {
                              data[key] = [data[key], value];
                         }
                    } else {
                         data[key] = value;
                    }
               }

               localStorage.setItem('pepMoveSurveyDraft', JSON.stringify(data));
          }

          // Load saved form data
          function loadFormData() {
               const savedData = localStorage.getItem('pepMoveSurveyDraft');
               if (savedData) {
                    const data = JSON.parse(savedData);

                    Object.keys(data).forEach(key => {
                         const elements = form.querySelectorAll(`[name="${key}"]`);

                         elements.forEach(element => {
                              if (element.type === 'checkbox' || element.type === 'radio') {
                                   if (Array.isArray(data[key])) {
                                        element.checked = data[key].includes(element.value);
                                   } else {
                                        element.checked = element.value === data[key];
                                   }
                              } else {
                                   element.value = data[key];
                              }
                         });
                    });
               }
          }

          // Load saved data on page load
          loadFormData();

          // Save data on input change
          inputs.forEach(input => {
               input.addEventListener('change', saveFormData);
          });

          // Clear saved data after successful submission
          if (window.location.search.includes('success')) {
               localStorage.removeItem('pepMoveSurveyDraft');
               document.getElementById('successMessage').style.display = 'block';
               form.style.display = 'none';
          }
     </script>
</body>

</html>