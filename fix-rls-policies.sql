-- Fix RLS policies to allow anonymous submissions
-- Run this in your Supabase SQL editor

-- STEP 1: Completely disable <PERSON><PERSON> for testing
ALTER TABLE survey_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback DISABLE ROW LEVEL SECURITY;

-- STEP 2: Drop ALL existing policies completely
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON survey_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON survey_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON survey_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON survey_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON task_frequency_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON task_frequency_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON task_frequency_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON task_frequency_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON task_comfort_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON task_comfort_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON task_comfort_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON task_comfort_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON market_coverage_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON market_coverage_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON market_coverage_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON market_coverage_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON work_schedule_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON work_schedule_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON work_schedule_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON work_schedule_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON survey_feedback;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON survey_feedback;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON survey_feedback;
DROP POLICY IF EXISTS "Allow authenticated reads" ON survey_feedback;

-- STEP 3: For testing purposes, keep RLS DISABLED
-- This will allow all operations without policy restrictions
-- Once we confirm the survey works, we can re-enable with proper policies

-- Verify RLS is disabled (should show 'f' for false)
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE tablename IN ('survey_responses', 'task_frequency_responses', 'task_comfort_responses', 'market_coverage_responses', 'work_schedule_responses', 'survey_feedback');
