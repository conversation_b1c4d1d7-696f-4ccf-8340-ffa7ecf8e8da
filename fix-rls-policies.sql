-- Fix RLS policies to allow anonymous submissions
-- Run this in your Supabase SQL editor

-- First, let's disable <PERSON><PERSON> temporarily to test
ALTER TABLE survey_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses DISABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback DISABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Enable read access for authenticated users" ON survey_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON survey_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON survey_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON survey_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON task_frequency_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON task_frequency_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON task_frequency_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON task_frequency_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON task_comfort_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON task_comfort_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON task_comfort_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON task_comfort_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON market_coverage_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON market_coverage_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON market_coverage_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON market_coverage_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON work_schedule_responses;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON work_schedule_responses;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON work_schedule_responses;
DROP POLICY IF EXISTS "Allow authenticated reads" ON work_schedule_responses;

DROP POLICY IF EXISTS "Enable read access for authenticated users" ON survey_feedback;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON survey_feedback;
DROP POLICY IF EXISTS "Allow anonymous inserts" ON survey_feedback;
DROP POLICY IF EXISTS "Allow authenticated reads" ON survey_feedback;

-- Re-enable RLS with proper policies
ALTER TABLE survey_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_frequency_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comfort_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE market_coverage_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE work_schedule_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE survey_feedback ENABLE ROW LEVEL SECURITY;

-- Create new policies that allow anonymous inserts
CREATE POLICY "Allow anonymous inserts" ON survey_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON survey_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow anonymous inserts" ON task_frequency_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON task_frequency_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow anonymous inserts" ON task_comfort_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON task_comfort_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow anonymous inserts" ON market_coverage_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON market_coverage_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow anonymous inserts" ON work_schedule_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON work_schedule_responses
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Allow anonymous inserts" ON survey_feedback
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated reads" ON survey_feedback
    FOR SELECT USING (auth.role() = 'authenticated');
