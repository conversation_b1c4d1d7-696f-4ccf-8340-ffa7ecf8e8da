// PepMove Survey Supabase Integration
// This script handles form submission to Supabase database

// Supabase configuration
const SUPABASE_URL = 'https://jjcnqzuwalnxgnvuebsx.supabase.co';
const SUPABASE_ANON_KEY = 'YOUR_ANON_KEY_HERE'; // You'll need to get this from Supabase dashboard

// Initialize Supabase client (you'll need to include the Supabase JS library)
// Add this to your HTML head: <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
const { createClient } = supabase;
const supabaseClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Enhanced form submission handler
async function submitSurveyToSupabase(formData) {
    try {
        // Show loading state
        const submitBtn = document.getElementById('submitBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        
        submitBtn.disabled = true;
        loading.style.display = 'block';
        errorMessage.style.display = 'none';
        successMessage.style.display = 'none';

        // Extract form data
        const personalInfo = {
            name: formData.get('name'),
            email: formData.get('email'),
            position: formData.get('position'),
            experience: formData.get('experience'),
            ip_address: await getUserIP(),
            user_agent: navigator.userAgent,
            submission_source: 'web_form'
        };

        // Insert main survey response
        const { data: surveyResponse, error: surveyError } = await supabaseClient
            .from('survey_responses')
            .insert([personalInfo])
            .select()
            .single();

        if (surveyError) throw surveyError;

        const surveyId = surveyResponse.id;

        // Insert task frequency responses
        const frequencyData = {
            survey_id: surveyId,
            freq_labeling: formData.get('freq_labeling'),
            freq_warehouse: formData.get('freq_warehouse'),
            freq_carrier: formData.get('freq_carrier'),
            freq_quality: formData.get('freq_quality'),
            freq_systems: formData.get('freq_systems'),
            freq_reporting: formData.get('freq_reporting'),
            freq_safety: formData.get('freq_safety'),
            freq_coordination: formData.get('freq_coordination'),
            freq_improvement: formData.get('freq_improvement')
        };

        const { error: frequencyError } = await supabaseClient
            .from('task_frequency_responses')
            .insert([frequencyData]);

        if (frequencyError) throw frequencyError;

        // Insert task comfort responses
        const comfortData = {
            survey_id: surveyId,
            comfort_labeling: formData.get('comfort_labeling'),
            comfort_warehouse: formData.get('comfort_warehouse'),
            comfort_carrier: formData.get('comfort_carrier'),
            comfort_quality: formData.get('comfort_quality'),
            comfort_systems: formData.get('comfort_systems'),
            comfort_reporting: formData.get('comfort_reporting'),
            comfort_safety: formData.get('comfort_safety'),
            comfort_coordination: formData.get('comfort_coordination'),
            comfort_improvement: formData.get('comfort_improvement')
        };

        const { error: comfortError } = await supabaseClient
            .from('task_comfort_responses')
            .insert([comfortData]);

        if (comfortError) throw comfortError;

        // Insert market coverage responses
        const markets = formData.getAll('markets');
        if (markets.length > 0) {
            const marketData = markets.map(market => ({
                survey_id: surveyId,
                market: market
            }));

            const { error: marketError } = await supabaseClient
                .from('market_coverage_responses')
                .insert(marketData);

            if (marketError) throw marketError;
        }

        // Insert work schedule responses
        const schedules = formData.getAll('schedule');
        if (schedules.length > 0) {
            const scheduleData = schedules.map(schedule => ({
                survey_id: surveyId,
                schedule_period: schedule
            }));

            const { error: scheduleError } = await supabaseClient
                .from('work_schedule_responses')
                .insert(scheduleData);

            if (scheduleError) throw scheduleError;
        }

        // Insert feedback
        const feedbackData = {
            survey_id: surveyId,
            support_tools: formData.get('support_tools'),
            comments: formData.get('comments')
        };

        const { error: feedbackError } = await supabaseClient
            .from('survey_feedback')
            .insert([feedbackData]);

        if (feedbackError) throw feedbackError;

        // Success - clear saved data and show success message
        localStorage.removeItem('pepMoveSurveyDraft');
        loading.style.display = 'none';
        successMessage.style.display = 'block';
        document.getElementById('surveyForm').style.display = 'none';

        // Optional: Still send email notification
        await sendEmailNotification(formData);

        return { success: true, surveyId: surveyId };

    } catch (error) {
        console.error('Error submitting survey:', error);
        
        // Hide loading and show error
        document.getElementById('loading').style.display = 'none';
        document.getElementById('errorMessage').style.display = 'block';
        document.getElementById('submitBtn').disabled = false;

        return { success: false, error: error.message };
    }
}

// Helper function to get user IP (optional)
async function getUserIP() {
    try {
        const response = await fetch('https://api.ipify.org?format=json');
        const data = await response.json();
        return data.ip;
    } catch (error) {
        console.warn('Could not get user IP:', error);
        return null;
    }
}

// Optional: Send email notification via FormSubmit as backup
async function sendEmailNotification(formData) {
    try {
        const response = await fetch('https://formsubmit.co/<EMAIL>', {
            method: 'POST',
            body: formData
        });
        return response.ok;
    } catch (error) {
        console.warn('Email notification failed:', error);
        return false;
    }
}

// Enhanced form event listener
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('surveyForm');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault(); // Prevent default form submission
        
        const formData = new FormData(form);
        const result = await submitSurveyToSupabase(formData);
        
        if (result.success) {
            console.log('Survey submitted successfully with ID:', result.surveyId);
        } else {
            console.error('Survey submission failed:', result.error);
        }
    });
});

// Analytics and dashboard functions
async function getSurveyStats() {
    try {
        // Get total responses
        const { count: totalResponses } = await supabaseClient
            .from('survey_responses')
            .select('*', { count: 'exact', head: true });

        // Get responses by experience level
        const { data: experienceData } = await supabaseClient
            .from('survey_responses')
            .select('experience')
            .not('experience', 'is', null);

        // Get most common markets
        const { data: marketData } = await supabaseClient
            .from('market_coverage_responses')
            .select('market, count(*)')
            .group('market')
            .order('count', { ascending: false });

        return {
            totalResponses,
            experienceBreakdown: experienceData,
            marketBreakdown: marketData
        };
    } catch (error) {
        console.error('Error fetching survey stats:', error);
        return null;
    }
}

// Export survey data to CSV
async function exportSurveyData() {
    try {
        const { data: surveyData } = await supabaseClient
            .from('survey_summary')
            .select('*')
            .order('created_at', { ascending: false });

        if (!surveyData || surveyData.length === 0) {
            alert('No survey data to export');
            return;
        }

        // Convert to CSV
        const headers = Object.keys(surveyData[0]);
        const csvContent = [
            headers.join(','),
            ...surveyData.map(row =>
                headers.map(header =>
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ].join('\n');

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `pepmove-survey-data-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

    } catch (error) {
        console.error('Error exporting survey data:', error);
        alert('Error exporting data. Please try again.');
    }
}

// Real-time subscription to new survey submissions
function subscribeToNewSubmissions(callback) {
    const subscription = supabaseClient
        .channel('survey_responses')
        .on('postgres_changes',
            { event: 'INSERT', schema: 'public', table: 'survey_responses' },
            callback
        )
        .subscribe();

    return subscription;
}
